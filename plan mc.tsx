

"use client";

import { useState, useEffect, useMemo, useTransition } from "react";
import { format } from "date-fns";
import { vi } from 'date-fns/locale';
import { Calendar as CalendarIcon, Loader2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";
import type { ProductionTask, TaskType } from "@/lib/types";
import { reportTasksAction } from "@/app/actions";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

// Mock API and data
const mockTasks: ProductionTask[] = [
    { id: 'RFID-20240730-1', planDate: '2024-07-30', line: 'L1', plan: 'Main', bo: 'BO123', printer: 'P1', stockCodeF: 'SC-F-001', smallSheets: 1000, largeSheets: 500, unroll: 20, stockSize: '1000x1200', note: '', status: 'Chờ cắt', reporters: [], lastReportTime: undefined, taskType: 'Cắt' },
    { id: 'RFID-20240730-2', planDate: '2024-07-30', line: 'L2', plan: 'Buffer', bo: 'BO124', printer: 'P2', stockCodeF: 'SC-F-002', smallSheets: 2500, largeSheets: 1250, unroll: 50, stockSize: '800x1000', note: 'Ưu tiên', status: 'Chờ cắt', reporters: [], lastReportTime: undefined, taskType: 'Cắt' },
    { id: 'RFID-20240730-3', planDate: '2024-07-30', line: 'L1', plan: 'Main', bo: 'BO125', printer: 'P1', stockCodeF: 'SC-F-003', smallSheets: 800, largeSheets: 400, unroll: 16, stockSize: '1000x1200', note: '', status: 'Partial', reporters: ['An'], lastReportTime: '2024-07-29T14:00:00Z', taskType: 'Xả' },
    { id: 'RFID-20240729-4', planDate: '2024-07-29', line: 'L3', plan: 'Draft', bo: 'BO126', printer: 'P3', stockCodeF: 'SC-F-004', smallSheets: 5000, largeSheets: 2500, unroll: 100, stockSize: '1200x1500', note: '', status: 'Done', reporters: ['Bình', 'Cường'], lastReportTime: '2024-07-29T10:00:00Z', taskType: 'Cắt' },
];

const employeeNames = ["Trần Hoàng Bảo", "Lê Trọng Nghĩa", "Võ Hoàng Khanh", "Nguyễn Hồng Ân", "Phạm Phong Phú", "Nguyễn Ngọc Danh", "Nguyễn Ngọc Hào", "Phạm Minh Kha", "Lê Minh Hùng", "Nguyễn Văn Liêm", "Nguyễn Chí Nhiên Đăng", "Nguyễn Trí Thức", "Nguyễn Ngọc Huệ", "Lê Văn Kiệt"];

async function getTasks(taskType: TaskType, filters: any): Promise<ProductionTask[]> {
  await new Promise(r => setTimeout(r, 300));
  let filteredTasks = mockTasks.filter(t => t.taskType === taskType);
  if (filters.date) {
    const selectedDate = filters.date as Date;
    // Get the year, month, and day from the local timezone of the selected date
    const localYear = selectedDate.getFullYear();
    const localMonth = selectedDate.getMonth();
    const localDay = selectedDate.getDate();

    filteredTasks = filteredTasks.filter(t => {
      // Create a date object from the task's date string.
      // Appending 'T00:00:00' ensures it's parsed as local time, not UTC.
      const taskDate = new Date(`${t.planDate}T00:00:00`);

      return taskDate.getFullYear() === localYear &&
             taskDate.getMonth() === localMonth &&
             taskDate.getDate() === localDay;
    });
  }
  if (filters.line && filters.line !== 'all') {
    filteredTasks = filteredTasks.filter(t => t.line === filters.line);
  }
  if (filters.plan && filters.plan !== 'all') {
    filteredTasks = filteredTasks.filter(t => t.plan === filters.plan);
  }
  if (filters.printer && filters.printer !== 'all') {
    filteredTasks = filteredTasks.filter(t => t.printer === filters.printer);
  }
  if (filters.stockCodeF && filters.stockCodeF !== 'all') {
    filteredTasks = filteredTasks.filter(t => t.stockCodeF === filters.stockCodeF);
  }
   if (filters.stockSize && filters.stockSize !== 'all') {
    filteredTasks = filteredTasks.filter(t => t.stockSize === filters.stockSize);
  }
  if (filters.status && filters.status !== 'all') {
    filteredTasks = filteredTasks.filter(t => t.status === filters.status);
  }
  return filteredTasks;
}

interface ProductionPlanProps {
  taskType: TaskType;
}

export function ProductionPlan({ taskType }: ProductionPlanProps) {
  const [filters, setFilters] = useState({ date: undefined as Date | undefined, line: 'all', plan: 'all', printer: 'all', stockCodeF: 'all', stockSize: 'all', status: 'all' });
  const [tasks, setTasks] = useState<ProductionTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTasks, setSelectedTasks] = useState<Record<string, boolean>>({});
  const [reporters, setReporters] = useState<Record<string, string>>({});
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();
  const [isReporterDialogOpen, setIsReporterDialogOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);


  const uniqueLines = useMemo(() => [...new Set(mockTasks.map(task => task.line))], []);
  const uniquePlans = useMemo(() => [...new Set(mockTasks.map(task => task.plan))], []);
  const uniquePrinters = useMemo(() => [...new Set(mockTasks.map(task => task.printer))], []);
  const uniqueStockCodes = useMemo(() => [...new Set(mockTasks.map(task => task.stockCodeF))], []);
  const uniqueStockSizes = useMemo(() => [...new Set(mockTasks.map(task => task.stockSize))], []);
  const uniqueStatuses = useMemo(() => [...new Set(mockTasks.map(task => task.status))], []);


  useEffect(() => {
    const fetchTasks = async () => {
      setIsLoading(true);
      const data = await getTasks(taskType, filters);
      setTasks(data);
      setIsLoading(false);
      setSelectedTasks({});
    };
    fetchTasks();
  }, [filters, taskType]);

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };
  
  const handleDateSelect = (date: Date | undefined) => {
    setFilters(prev => ({ ...prev, date: date }));
  }

  const selectionTotals = useMemo(() => {
    return tasks.reduce(
      (acc, task) => {
        if (selectedTasks[task.id]) {
          acc.orders += 1;
          acc.smallSheets += task.smallSheets;
        }
        return acc;
      },
      { orders: 0, smallSheets: 0 }
    );
  }, [selectedTasks, tasks]);
  
  const handleReport = (reportType: 'Partial' | 'Complete') => {
      const selectedReporterNames = Object.values(reporters).filter(name => name && name !== 'none');

      if(selectionTotals.orders === 0) {
          toast({ variant: "destructive", title: "Chưa chọn công việc", description: "Vui lòng chọn ít nhất một công việc để báo cáo." });
          return;
      }
      if(selectedReporterNames.length === 0) {
          toast({ variant: "destructive", title: "Chưa chọn người báo cáo", description: "Vui lòng chọn ít nhất một người báo cáo." });
          return;
      }

      startTransition(async () => {
        const formData = new FormData();
        Object.keys(selectedTasks).forEach(taskId => {
            if(selectedTasks[taskId]) formData.append('taskIds', taskId);
        });
        selectedReporterNames.forEach(name => {
            formData.append('reporterName', name);
        });
        formData.append('reportType', reportType);

        const result = await reportTasksAction(formData);
        if(result.success) {
            toast({ title: "Báo cáo thành công!", description: `Đã báo cáo ${reportType === 'Complete' ? 'hoàn thành' : 'một phần'} cho ${selectionTotals.orders} công việc.` });
            setSelectedTasks({});
            setReporters({});
            setIsReporterDialogOpen(false); // Close dialog on success
            // Refetch data
            setIsLoading(true);
            const data = await getTasks(taskType, filters);
            setTasks(data);
            setIsLoading(false);
        } else {
             toast({ variant: "destructive", title: "Báo cáo thất bại", description: "Đã có lỗi xảy ra. Vui lòng thử lại." });
        }
      });
  }

  const handleReporterChange = (cutter: string, name: string) => {
      setReporters(prev => ({...prev, [cutter]: name}));
  }

  const title = taskType === 'Cắt' ? "Plan Cắt Sản xuất" : "Plan Xả Sản xuất";
  const allSelected = tasks.length > 0 && tasks.every(t => selectedTasks[t.id] || t.status === 'Done');
  
  const formattedDate = useMemo(() => {
    if (!filters.date) return "Chọn ngày";
    try {
        return format(filters.date, "dd/MM/yyyy");
    } catch (error) {
        return "Ngày không hợp lệ";
    }
  }, [filters.date]);


  if (!isClient) {
    return null; // Or a loading spinner
  }

  return (
    <div className="flex flex-col h-full">
      <Card className="flex-grow flex flex-col">
        <CardHeader>
          <CardTitle className="text-center uppercase">{title}</CardTitle>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 items-center gap-2 pt-4">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant={"outline"} className="w-full justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  <span>{formattedDate}</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0"><Calendar locale={vi} mode="single" selected={filters.date} onSelect={handleDateSelect} initialFocus /></PopoverContent>
            </Popover>
             <Select value={filters.line} onValueChange={(value) => handleFilterChange('line', value)}>
                <SelectTrigger>
                    <SelectValue placeholder="Line" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Tất cả Line</SelectItem>
                    {uniqueLines.map(line => <SelectItem key={line} value={line}>{line}</SelectItem>)}
                </SelectContent>
            </Select>
            <Select value={filters.plan} onValueChange={(value) => handleFilterChange('plan', value)}>
                <SelectTrigger>
                    <SelectValue placeholder="Plan" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Tất cả Plan</SelectItem>
                    {uniquePlans.map(plan => <SelectItem key={plan} value={plan}>{plan}</SelectItem>)}
                </SelectContent>
            </Select>
            <Select value={filters.printer} onValueChange={(value) => handleFilterChange('printer', value)}>
                <SelectTrigger>
                    <SelectValue placeholder="Máy in" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Tất cả máy in</SelectItem>
                    {uniquePrinters.map(printer => <SelectItem key={printer} value={printer}>{printer}</SelectItem>)}
                </SelectContent>
            </Select>
            <Select value={filters.stockCodeF} onValueChange={(value) => handleFilterChange('stockCodeF', value)}>
                <SelectTrigger>
                    <SelectValue placeholder="Stock code F" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Tất cả stock code</SelectItem>
                    {uniqueStockCodes.map(code => <SelectItem key={code} value={code}>{code}</SelectItem>)}
                </SelectContent>
            </Select>
            <Select value={filters.stockSize} onValueChange={(value) => handleFilterChange('stockSize', value)}>
                <SelectTrigger>
                    <SelectValue placeholder="Stock size" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Tất cả stock size</SelectItem>
                    {uniqueStockSizes.map(size => <SelectItem key={size} value={size}>{size}</SelectItem>)}
                </SelectContent>
            </Select>
            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger>
                    <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="all">Tất cả trạng thái</SelectItem>
                    {uniqueStatuses.map(status => <SelectItem key={status} value={status}>{status}</SelectItem>)}
                </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent className="flex-grow overflow-auto">
          <Table>
            <TableHeader className="sticky top-0 bg-background z-10">
              <TableRow className="bg-red-500 hover:bg-red-500/90">
                <TableHead className="w-[50px] text-white">
                  <Checkbox 
                    checked={allSelected}
                    onCheckedChange={(checked) => {
                        const newSelected: Record<string, boolean> = {};
                        if(checked) {
                            tasks.forEach(t => { if(t.status !== 'Done') newSelected[t.id] = true; });
                        }
                        setSelectedTasks(newSelected);
                    }}
                    className="border-white data-[state=checked]:bg-white data-[state=checked]:text-red-500"
                  />
                </TableHead>
                <TableHead className="text-white">Kế Hoạch Ngày</TableHead>
                <TableHead className="text-white">Line</TableHead>
                <TableHead className="text-white">Plan</TableHead>
                <TableHead className="text-white">ID</TableHead>
                <TableHead className="text-white">BO</TableHead>
                <TableHead className="text-white">Máy in</TableHead>
                <TableHead className="text-white">Stock code F</TableHead>
                <TableHead className="text-white">Tờ nhỏ</TableHead>
                <TableHead className="text-white">Tờ lớn</TableHead>
                <TableHead className="text-white">Xả</TableHead>
                <TableHead className="text-white">Stock size</TableHead>
                <TableHead className="text-white">Trạng thái</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow><TableCell colSpan={13} className="h-24 text-center"><Loader2 className="mx-auto h-8 w-8 animate-spin text-primary" /></TableCell></TableRow>
              ) : tasks.length === 0 ? (
                 <TableRow><TableCell colSpan={13} className="h-24 text-center">Không có công việc nào.</TableCell></TableRow>
              ) : (
                tasks.map((task) => (
                  <TableRow key={task.id} data-state={selectedTasks[task.id] && "selected"} className={cn(task.status === 'Done' && "opacity-50 text-muted-foreground")}>
                    <TableCell>
                      <Checkbox
                        disabled={task.status === 'Done'}
                        checked={selectedTasks[task.id] || false}
                        onCheckedChange={(checked) => setSelectedTasks(prev => ({...prev, [task.id]: !!checked}))}
                      />
                    </TableCell>
                    <TableCell>{task.planDate}</TableCell>
                    <TableCell>{task.line}</TableCell>
                    <TableCell>{task.plan}</TableCell>
                    <TableCell className="font-medium">{task.id}</TableCell>
                    <TableCell>{task.bo}</TableCell>
                    <TableCell>{task.printer}</TableCell>
                    <TableCell>{task.stockCodeF}</TableCell>
                    <TableCell>{task.smallSheets.toLocaleString()}</TableCell>
                    <TableCell>{task.largeSheets.toLocaleString()}</TableCell>
                    <TableCell>{task.unroll.toLocaleString()}</TableCell>
                    <TableCell>{task.stockSize}</TableCell>
                    <TableCell>{task.status}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      <div className="sticky bottom-0 mt-4 bg-card border rounded-lg p-4 shadow-lg">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-4 text-sm">
                <div>Đã chọn: <span className="font-bold">{selectionTotals.orders}</span> đơn hàng</div>
                <div className="h-4 w-px bg-border"></div>
                <div>Tổng tờ nhỏ: <span className="font-bold">{selectionTotals.smallSheets.toLocaleString()}</span></div>
            </div>
            <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
                 <Dialog open={isReporterDialogOpen} onOpenChange={setIsReporterDialogOpen}>
                    <DialogTrigger asChild>
                       <Button className="w-full sm:w-auto">Báo cáo</Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Chọn người báo cáo và thực hiện</DialogTitle>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                            {Array.from({ length: 6 }, (_, i) => i + 1).map(num => (
                                <div key={num} className="grid grid-cols-4 items-center gap-4">
                                    <Label htmlFor={`cutter-${num}`} className="text-right">
                                        Thợ máy cắt {num}
                                    </Label>
                                    <Select 
                                        value={reporters[`cutter${num}`] || 'none'} 
                                        onValueChange={(value) => handleReporterChange(`cutter${num}`, value)}
                                    >
                                        <SelectTrigger className="col-span-3">
                                            <SelectValue placeholder="Chọn nhân viên" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">-- Chưa chọn --</SelectItem>
                                            {employeeNames.map(name => <SelectItem key={name} value={name}>{name}</SelectItem>)}
                                        </SelectContent>
                                    </Select>
                                </div>
                            ))}
                        </div>
                        <DialogFooter>
                             <div className="flex gap-2 w-full">
                                <Button 
                                    variant="outline" 
                                    className="w-full"
                                    onClick={() => handleReport('Partial')}
                                    disabled={isPending || selectionTotals.orders === 0}
                                >
                                    {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    Báo cáo một phần
                                </Button>
                                <Button 
                                    className="w-full"
                                    onClick={() => handleReport('Complete')}
                                    disabled={isPending || selectionTotals.orders === 0}
                                >
                                    {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    Báo cáo hoàn thành
                                </Button>
                            </div>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
          </div>
      </div>
    </div>
  );
}

    

    


